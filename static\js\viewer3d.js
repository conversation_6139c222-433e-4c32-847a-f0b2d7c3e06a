/**
 * 3D Viewer Module for displaying OBJ files using Three.js
 */

import * as THREE from "three";
import { OBJLoader } from "three/addons/loaders/OBJLoader.js";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";

class Viewer3D {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;
    this.currentModel = null;
    this.currentObjPath = null;
    this.isWireframe = false;

    this.loadingElement = document.getElementById("viewer-loading");
    this.errorElement = document.getElementById("viewer-error");

    this.init();
  }

  init() {
    if (!this.container) {
      console.error("3D Viewer container not found");
      return;
    }

    console.log("🎯 Container found:", this.container);

    // Ensure container has dimensions
    if (this.container.clientWidth === 0 || this.container.clientHeight === 0) {
      this.container.style.minWidth = "800px";
      this.container.style.minHeight = "600px";
      console.log("🎯 Applied minimum dimensions to container with zero size");
    }

    console.log(
      "🎯 Container dimensions:",
      this.container.clientWidth,
      "x",
      this.container.clientHeight
    );

    // Check if Three.js is loaded
    if (typeof THREE === "undefined") {
      console.error("Three.js library not loaded");
      this.showError("3D viewer library not loaded");
      return;
    }

    // Create scene
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x2a2a2a);
    console.log("🎯 Scene created");

    // Create camera with better default position for visibility
    const width = this.container.clientWidth || 800;
    const height = this.container.clientHeight || 600;
    this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    this.camera.position.set(5, 5, 5);
    this.camera.lookAt(0, 0, 0);
    console.log("🎯 Camera created with aspect ratio:", width / height);

    // Create renderer
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
    });
    this.renderer.setSize(width, height);
    this.renderer.setClearColor(0x2a2a2a, 1);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.container.appendChild(this.renderer.domElement);
    console.log("🎯 Renderer created and added to container");

    // Add lights
    this.setupLights();

    // Add controls with better default settings
    try {
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.1;
      this.controls.enableZoom = true;
      this.controls.enablePan = true;
      this.controls.autoRotate = true; // Rotate to help see the model
      this.controls.autoRotateSpeed = 1.0;
      console.log("🎯 OrbitControls initialized with auto-rotation");
    } catch (error) {
      console.warn(
        "OrbitControls not loaded - mouse controls will be limited",
        error
      );
    }

    // Handle window resize
    window.addEventListener("resize", () => this.onWindowResize());

    // Start render loop
    this.animate();

    // Add a test cube to verify the viewer works
    this.addTestCube();
  }

  setupLights() {
    // Ambient light - brighter for better base visibility
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
    this.scene.add(ambientLight);

    // Directional light - from front-top-right
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);

    // Second directional light - from back-top-left for better shape definition
    const backLight = new THREE.DirectionalLight(0xffffff, 0.4);
    backLight.position.set(-5, 8, -5);
    this.scene.add(backLight);

    // Point light - for additional highlights
    const pointLight = new THREE.PointLight(0xffffff, 0.5);
    pointLight.position.set(-10, 10, -10);
    this.scene.add(pointLight);

    console.log("🎯 Enhanced lighting setup completed");
  }

  addTestCube() {
    console.log("🎯 Adding test cube");
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshPhongMaterial({ color: 0xff0000 });
    const cube = new THREE.Mesh(geometry, material);
    cube.name = "testCube";
    this.scene.add(cube);
    console.log(
      "🎯 Test cube added, scene children:",
      this.scene.children.length
    );
  }

  removeTestCube() {
    const testCube = this.scene.getObjectByName("testCube");
    if (testCube) {
      this.scene.remove(testCube);
      console.log("🎯 Test cube removed");
    }
  }

  showLoading() {
    if (this.loadingElement) {
      this.loadingElement.classList.remove("hidden");
    }
    if (this.errorElement) {
      this.errorElement.classList.add("hidden");
    }
  }

  hideLoading() {
    if (this.loadingElement) {
      this.loadingElement.classList.add("hidden");
    }
  }

  showError(message = "Failed to load 3D model") {
    if (this.errorElement) {
      this.errorElement.querySelector("p").textContent = message;
      this.errorElement.classList.remove("hidden");
    }
    this.hideLoading();
  }

  hideError() {
    if (this.errorElement) {
      this.errorElement.classList.add("hidden");
    }
  }

  showSuccessMessage() {
    // Create temporary success message
    const successDiv = document.createElement("div");
    successDiv.className = "viewer-success";
    successDiv.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(34, 197, 94, 0.9);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      text-align: center;
      z-index: 60;
      animation: fadeInOut 2s ease-in-out;
    `;
    successDiv.innerHTML = `
      <i class="fas fa-check-circle text-xl mb-2"></i>
      <p>3D Model Loaded Successfully!</p>
    `;

    this.container.appendChild(successDiv);

    // Remove after animation
    setTimeout(() => {
      if (successDiv.parentNode) {
        successDiv.parentNode.removeChild(successDiv);
      }
    }, 2000);
  }

  loadOBJ(objPath) {
    this.showLoading();
    this.hideError();
    this.currentObjPath = objPath;

    // Remove existing model
    if (this.currentModel) {
      this.scene.remove(this.currentModel);
      this.currentModel = null;
    }

    // Load OBJ file
    const loader = new OBJLoader();

    loader.load(
      objPath,
      (object) => {
        this.onModelLoaded(object);
      },
      (progress) => {
        console.log("Loading progress:", progress);
      },
      (error) => {
        console.error("Error loading OBJ:", error);
        this.showError("Failed to load 3D model");
      }
    );
  }

  onModelLoaded(object) {
    console.log("🎯 onModelLoaded called with object:", object);

    // Remove test cube
    this.removeTestCube();

    this.currentModel = object;

    // Apply default material with better appearance
    object.traverse((child) => {
      if (child.isMesh) {
        console.log("🎯 Found mesh:", child);

        // Create a more visible material with bright color
        const material = new THREE.MeshStandardMaterial({
          color: 0xff5500, // Bright orange for visibility
          roughness: 0.3,
          metalness: 0.7,
          side: THREE.DoubleSide,
          emissive: 0x222222, // Slight self-illumination
          flatShading: false,
        });

        child.material = material;
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    // Center and scale the model
    this.centerAndScaleModel(object);

    // Add a ground plane for better depth perception
    this.addGroundPlane();

    // Add to scene
    this.scene.add(object);
    console.log(
      "🎯 Model added to scene. Scene children count:",
      this.scene.children.length
    );

    // Reset camera position for the best view
    this.resetCamera();

    // Set auto-rotation for better visibility
    if (this.controls) {
      this.controls.autoRotate = true;
    }

    this.hideLoading();
    console.log("🎯 3D model loaded successfully");

    // Show success message
    this.showSuccessMessage();

    // Force a render
    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
      console.log("🎯 Forced render completed");
    }
  }

  addGroundPlane() {
    // Remove any existing ground plane
    const existingPlane = this.scene.getObjectByName("groundPlane");
    if (existingPlane) {
      this.scene.remove(existingPlane);
    }

    // Create a simple ground plane for better depth perception
    const planeGeometry = new THREE.PlaneGeometry(20, 20);
    const planeMaterial = new THREE.MeshStandardMaterial({
      color: 0x555555,
      side: THREE.DoubleSide,
      roughness: 0.8,
      metalness: 0.2,
    });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = Math.PI / 2; // Rotate to be horizontal
    plane.position.y = -2; // Position below the model
    plane.receiveShadow = true;
    plane.name = "groundPlane";
    this.scene.add(plane);
    console.log("🎯 Ground plane added for better depth perception");
  }

  centerAndScaleModel(object) {
    // Calculate bounding box
    const box = new THREE.Box3().setFromObject(object);
    const center = box.getCenter(new THREE.Vector3());
    const size = box.getSize(new THREE.Vector3());

    console.log("🎯 Model bounding box:", {
      center: center,
      size: size,
      isEmpty: box.isEmpty(),
    });

    // Center the model
    object.position.sub(center);

    // Scale the model to fit in view
    const maxDim = Math.max(size.x, size.y, size.z);
    if (maxDim > 0) {
      // Use a smaller scale for better visibility
      const scale = 2 / maxDim;
      object.scale.setScalar(scale);
      console.log("🎯 Model scaled by:", scale);
    } else {
      // If maxDim is 0 or negative, use a default scale
      object.scale.setScalar(1);
      console.log("🎯 Model has invalid dimensions, using default scale");
    }
  }

  resetCamera() {
    if (this.camera && this.controls) {
      // Position camera at a better angle for viewing
      this.camera.position.set(3, 3, 3);
      this.camera.lookAt(0, 0, 0);
      this.controls.reset();
      console.log("🎯 Camera reset to position:", this.camera.position);
    }
  }

  toggleWireframe() {
    if (!this.currentModel) return;

    this.isWireframe = !this.isWireframe;

    this.currentModel.traverse((child) => {
      if (child.isMesh) {
        if (this.isWireframe) {
          // Create wireframe material
          child.material = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            wireframe: true,
            side: THREE.DoubleSide,
          });
        } else {
          // Restore solid material
          child.material = new THREE.MeshPhongMaterial({
            color: 0x4a90e2,
            shininess: 30,
            side: THREE.DoubleSide,
            transparent: false,
          });
        }
      }
    });

    console.log(`Wireframe mode: ${this.isWireframe ? "ON" : "OFF"}`);
  }

  downloadModel() {
    if (this.currentObjPath) {
      const link = document.createElement("a");
      link.href = this.currentObjPath;
      link.download = this.currentObjPath.split("/").pop();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  show() {
    console.log("🎯 show() called");
    if (this.container) {
      console.log("🎯 Container found, removing hidden class");
      this.container.classList.remove("hidden");

      // Apply fixed styles to ensure the container is visible
      this.container.style.position = "absolute";
      this.container.style.width = "100%";
      this.container.style.height = "100%";
      this.container.style.left = "0";
      this.container.style.top = "0";
      this.container.style.zIndex = "10";
      this.container.style.backgroundColor = "#2a2a2a";

      // Make sure parent element is visible and sized
      if (this.container.parentElement) {
        this.container.parentElement.style.position = "relative";
        this.container.parentElement.style.minHeight = "500px";
        this.container.parentElement.style.width = "100%";
        this.container.parentElement.style.display = "block";
        this.container.parentElement.style.visibility = "visible";
        console.log("🎯 Applied visibility fixes to parent element");
      }

      // Force a layout calculation
      const forceLayout = this.container.offsetWidth;

      console.log(
        "🎯 Container dimensions after style changes:",
        this.container.offsetWidth,
        "x",
        this.container.offsetHeight
      );

      // Hide default empty state
      const emptyState = document.getElementById("default-empty-state");
      if (emptyState) {
        console.log("🎯 Hiding empty state");
        emptyState.classList.add("hidden");
      }

      this.onWindowResize();
      console.log("🎯 Container shown and resized");
    } else {
      console.error("🎯 Container not found in show()");
    }
  }

  hide() {
    if (this.container) {
      this.container.classList.add("hidden");
      // Show default empty state
      const emptyState = document.getElementById("default-empty-state");
      if (emptyState) {
        emptyState.classList.remove("hidden");
      }
    }
  }

  onWindowResize() {
    if (!this.camera || !this.renderer || !this.container) {
      console.log("🎯 onWindowResize: Missing components", {
        camera: !!this.camera,
        renderer: !!this.renderer,
        container: !!this.container,
      });
      return;
    }

    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    console.log("🎯 onWindowResize: Resizing to", width, "x", height);

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);

    console.log("🎯 onWindowResize: Resize completed");
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    // Update controls if available
    if (this.controls) {
      this.controls.update();
    }

    // Make sure rendering happens
    if (this.renderer && this.scene && this.camera) {
      // Make sure camera is looking at the center
      if (this.currentModel) {
        this.camera.lookAt(0, 0, 0);
      }

      // Render the scene
      this.renderer.render(this.scene, this.camera);

      // Only log occasionally to reduce console spam
      if (!this.frameCount) this.frameCount = 0;
      this.frameCount++;
      if (this.frameCount === 60) {
        console.log(
          "🎯 Render loop active, scene children:",
          this.scene.children.length,
          "Renderer size:",
          this.renderer.domElement.width + "x" + this.renderer.domElement.height
        );
        this.frameCount = 0;
      }
    }
  }

  dispose() {
    if (this.renderer) {
      this.renderer.dispose();
    }
    if (this.controls) {
      this.controls.dispose();
    }
  }
}

// Global viewer instance
let viewer3D = null;

// Initialize viewer when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  console.log("🎯 DOM loaded, initializing 3D viewer");
  console.log("🎯 THREE available:", typeof THREE !== "undefined");
  console.log("🎯 OBJLoader available:", typeof OBJLoader !== "undefined");
  console.log(
    "🎯 OrbitControls available:",
    typeof OrbitControls !== "undefined"
  );

  viewer3D = new Viewer3D("viewer-3d-container");
  console.log("🎯 viewer3D initialized:", viewer3D);
});

// Global functions for controls
function resetCamera() {
  if (viewer3D) {
    viewer3D.resetCamera();
  }
}

function toggleWireframe() {
  if (viewer3D) {
    viewer3D.toggleWireframe();
  }
}

function downloadModel() {
  if (viewer3D) {
    viewer3D.downloadModel();
  }
}

// Function to load OBJ file (called from main.js)
function loadOBJModel(objPath) {
  console.log("🎯 loadOBJModel called with path:", objPath);
  if (viewer3D) {
    console.log("🎯 viewer3D exists, showing and loading OBJ");
    viewer3D.show();
    viewer3D.loadOBJ(objPath);
  } else {
    console.error("❌ viewer3D not initialized");
  }
}

// Function to hide 3D viewer
function hide3DViewer() {
  if (viewer3D) {
    viewer3D.hide();
  }
}

// Make functions available globally
window.loadOBJModel = loadOBJModel;
window.hide3DViewer = hide3DViewer;
window.resetCamera = resetCamera;
window.toggleWireframe = toggleWireframe;
window.downloadModel = downloadModel;
